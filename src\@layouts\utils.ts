import type { Router } from 'vue-router'
import { layoutConfig } from '@layouts/config'
import { AppContentLayoutNav } from '@layouts/enums'
import { useLayoutConfigStore } from '@layouts/stores/config'
import type { NavGroup, NavLink, NavLinkProps } from '@layouts/types'

export const openGroups = ref<string[]>([])

/**
 * Return nav link props to use
 // @param {Object, String} item navigation routeName or route Object provided in navigation data
 */

export const getComputedNavLinkToProp = computed(() => (link: NavLink) => {
  const props: NavLinkProps = {
    target: link.target,
    rel: link.rel,
  }

  // If route is string => it assumes string is route name => Create route object from route name
  // If route is not string => It assumes it's route object => returns passed route object
  if (link.to)
    props.to = typeof link.to === 'string' ? { name: link.to } : link.to
  else props.href = link.href

  return props
})

/**
 * Return route name for navigation link
 * If link is string then it will assume it is route-name
 * IF link is object it will resolve the object and will return the link
 // @param {Object, String} link navigation link object/string
 */
export const resolveNavLinkRouteName = (link: NavLink, router: Router) => {
  if (!link.to)
    return null

  if (typeof link.to === 'string')
    return link.to

  return router.resolve(link.to).name
}

/**
 * Check if nav-link is active
 * @param {object} link nav-link object
 */
export const isNavLinkActive = (link: NavLink, router: Router) => {
  // Matched routes array of current route
  const matchedRoutes = router.currentRoute.value.matched

  // Check if provided route matches route's matched route
  const resolveRoutedName = resolveNavLinkRouteName(link, router)

  if (!resolveRoutedName)
    return false

  return matchedRoutes.some(route => {
    return route.name === resolveRoutedName || route.meta.navActiveLink === resolveRoutedName
  })
}

/**
 * Check if nav group is active
 * @param {Array} children Group children
 */
export const isNavGroupActive = (children: (NavLink | NavGroup)[], router: Router): boolean =>
  children.some(child => {
    // If child have children => It's group => Go deeper(recursive)
    if ('children' in child)
      return isNavGroupActive(child.children, router)

    // else it's link => Check for matched Route
    return isNavLinkActive(child, router)
  })

/**
 * Change `dir` attribute based on direction
 * @param dir 'ltr' | 'rtl'
 */
export const _setDirAttr = (dir: 'ltr' | 'rtl') => {
  // Check if document exists for SSR
  if (typeof document !== 'undefined')
    document.documentElement.setAttribute('dir', dir)
}

/**
 * Return dynamic i18n props based on i18n plugin is enabled or not
 * @param key i18n translation key
 * @param tag tag to wrap the translation with
 */
export const getDynamicI18nProps = (key: string, tag = 'span') => {
  if (!layoutConfig.app.i18n.enable)
    return {}

  return {
    keypath: key,
    tag,
    scope: 'global',
  }
}

export const switchToVerticalNavOnLtOverlayNavBreakpoint = () => {
  const configStore = useLayoutConfigStore()

  /*
      ℹ️ This is flag will hold nav type need to render when switching between lgAndUp from mdAndDown window width

      Requirement: When we nav is set to `horizontal` and we hit the `mdAndDown` breakpoint nav type shall change to `vertical` nav
      Now if we go back to `lgAndUp` breakpoint from `mdAndDown` how we will know which was previous nav type in large device?

      Let's assign value of `appContentLayoutNav` as default value of lgAndUpNav. Why 🤔?
        If template is viewed in lgAndUp
          We will assign `appContentLayoutNav` value to `lgAndUpNav` because at this point both constant is same
          Hence, for `lgAndUpNav` it will take value from theme config file
        else
          It will always show vertical nav and if user increase the window width it will fallback to `appContentLayoutNav` value
          But `appContentLayoutNav` will be value set in theme config file
    */
  const lgAndUpNav = ref(configStore.appContentLayoutNav)

  /*
      There might be case where we manually switch from vertical to horizontal nav and vice versa in `lgAndUp` screen
      So when user comes back from `mdAndDown` to `lgAndUp` we can set updated nav type
      For this we need to update the `lgAndUpNav` value if screen is `lgAndUp`
    */
  watch(
    () => configStore.appContentLayoutNav,
    value => {
      if (!configStore.isLessThanOverlayNavBreakpoint)
        lgAndUpNav.value = value
    },
  )

  /*
      This is layout switching part
      If it's `mdAndDown` => We will use vertical nav no matter what previous nav type was
      Or if it's `lgAndUp` we need to switch back to `lgAndUp` nav type. For this we will tracker property `lgAndUpNav`
    */
  watch(() => configStore.isLessThanOverlayNavBreakpoint, val => {
    configStore.appContentLayoutNav = val ? AppContentLayoutNav.Vertical : lgAndUpNav.value
  }, { immediate: true })
}

/**
 * Convert Hex color to rgb
 * @param hex
 */

export const hexToRgb = (hex: string) => {
  // Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
  const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i

  hex = hex.replace(shorthandRegex, (m: string, r: string, g: string, b: string) => {
    return r + r + g + g + b + b
  })

  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)

  return result ? `${Number.parseInt(result[1], 16)},${Number.parseInt(result[2], 16)},${Number.parseInt(result[3], 16)}` : null
}

/**
 *RGBA color to Hex color with / without opacity
 */
export const rgbaToHex = (rgba: string, forceRemoveAlpha = false) => {
  return (
    `#${rgba
      .replace(/^rgba?\(|\s+|\)$/g, '') // Get's rgba / rgb string values
      .split(',') // splits them at ","
      .filter((string, index) => !forceRemoveAlpha || index !== 3)
      .map(string => Number.parseFloat(string)) // Converts them to numbers
      .map((number, index) => (index === 3 ? Math.round(number * 255) : number)) // Converts alpha to 255 number
      .map(number => number.toString(16)) // Converts numbers to hex
      .map(string => (string.length === 1 ? `0${string}` : string)) // Adds 0 when length of one number is 1
      .join('')}`
  )
}

// Generate random UUID
export const getRandomUUID = () => {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID().split('-').join('')
  }
  else {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)

      return v.toString(16)
    }).split('-').join('')
  }
}

/**
 * 验证字符串的字节长度，中文字符算3个字节，其他字符算1个字节
 * @param str 要验证的字符串
 * @returns 字节长度
 */
export function getSignalStrengthPercentage(signalStrength: number | string): string | number {
  // 检查是否为特殊值
  if (signalStrength === '1G')
    return '1GbE'
  else if (signalStrength === '2.5G')
    return '2.5GbE'

  signalStrength = Number(signalStrength)

  // 确定信号强度的范围
  const minDbm = -100 // 最小信号强度（dBm）
  const maxDbm = -30 // 最大信号强度（dBm）

  // 如果信号强度超出范围，返回 0 或 100
  if (typeof signalStrength !== 'number' || isNaN(signalStrength))
    throw new Error('Invalid signal strength value')

  if (signalStrength <= minDbm)
    return 0
  else if (signalStrength >= maxDbm)
    return 100

  // 计算百分比
  let percentage = 0
  if (signalStrength >= -30) {
    percentage = 100
  }
  else if (signalStrength >= -50) {
    // 线性映射 -30 到 -50dBm 到 80-100%
    percentage = 80 + ((signalStrength + 50) / 20) * 20
  }
  else if (signalStrength >= -70) {
    // 线性映射 -50 到 -70dBm 到 40-80%
    percentage = 40 + ((signalStrength + 70) / 20) * 40
  }
  else if (signalStrength >= -85) {
    // 线性映射 -70 到 -85dBm 到 10-40%
    percentage = 10 + ((signalStrength + 85) / 15) * 30
  }
  else {
    // -85dBm 以下：0-10%
    percentage = Math.max(0, ((signalStrength + 100) / 15) * 10)
  }

  // 确保百分比在0-100之间
  percentage = Math.min(100, Math.max(0, percentage))

  // 返回四舍五入后的百分比
  return Math.round(percentage)
}

export function rssiToPercentage(rssi: number): number {
  const percentage = getSignalStrengthPercentage(rssi)
  let num = 1
  if (percentage >= 80) {
    num = 4
  }
  else if (percentage >= 60) {
    // 线性映射 -30 到 -50dBm 到 80-100%
    num = 3
  }
  else if (percentage >= 40) {
    // 线性映射 -50 到 -70dBm 到 40-80%
    num = 2
  }
  else {
    // -85dBm 以下：0-10%
    num = 1
  }

  return num
}

/**
 * 验证字符串的字节长度，中文字符算3个字节，其他字符算1个字节
 * @param str 要验证的字符串
 * @returns 字节长度
 */
export const isByteLengthInRange = (str: string, min: number, max: number): boolean => {
  let textLength = 0
  for (const char of str)
    textLength += /[\u4E00-\u9FA5]/.test(char) ? 3 : 1

  return textLength >= min && textLength <= max
}

/**
 * 验证MAC地址是否合法
 * @param mac 要验证的MAC地址字符串
 * @returns 是否为合法的MAC地址
 */
export const isValidMacAddress = (mac: string): boolean => {
  // 支持格式: 00:1A:2B:3C:4D:5E 或 00-1A-2B-3C-4D-5E 或 001A.2B3C.4D5E
  const macRegex = /^([0-9A-F]{2}([-:])){5}([0-9A-F]{2})$|^([0-9A-F]{4}\.){2}([0-9A-F]{4})$/i

  return macRegex.test(mac)
}

/**
 * 验证IPv4地址是否合法
 * @param ip 要验证的IPv4地址字符串
 * @returns 是否为合法的IPv4地址
 */
export const isValidIPv4 = (ip: string): boolean => {
  const ipv4Regex = /^(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d{2}|[1-9]?\d)){3}$/

  return ipv4Regex.test(ip)
}

/**
 * 验证IPv6地址是否合法
 * @param ipv6 要验证的IPv6地址字符串
 * @returns 是否为合法的IPv6地址
 */
export const isValidIPv6 = (ipv6: string): boolean => {
  if (!ipv6 || ipv6.trim() === '')
    return false

  const trimmedIpv6 = ipv6.trim()

  // IPv6正则表达式，支持完整格式和压缩格式
  const ipv6Regex = /^(?:(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|(?:[0-9a-fA-F]{1,4}:){1,7}:|(?:[0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|(?:[0-9a-fA-F]{1,4}:){1,5}(?::[0-9a-fA-F]{1,4}){1,2}|(?:[0-9a-fA-F]{1,4}:){1,4}(?::[0-9a-fA-F]{1,4}){1,3}|(?:[0-9a-fA-F]{1,4}:){1,3}(?::[0-9a-fA-F]{1,4}){1,4}|(?:[0-9a-fA-F]{1,4}:){1,2}(?::[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:(?::[0-9a-fA-F]{1,4}){1,6}|:(?:(?::[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(?::[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]+|::(?:ffff(?::0{1,4})?:)?(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d)|(?:[0-9a-fA-F]{1,4}:){1,4}:(?:(?:25[0-5]|(?:2[0-4]|1?\d)?\d)\.){3}(?:25[0-5]|(?:2[0-4]|1?\d)?\d))$/

  return ipv6Regex.test(trimmedIpv6)
}

/**
 * 验证域名是否合法
 * @param domain 要验证的域名字符串
 * @returns 是否为合法的域名
 */
export const isValidDomain = (domain: string): boolean => {
  if (!domain || domain.trim() === '')
    return false

  const trimmedDomain = domain.trim()

  // 域名长度限制：总长度不超过253字符，每个标签不超过63字符
  if (trimmedDomain.length > 253)
    return false

  // 域名不能以点开头或结尾（除了根域名）
  if (trimmedDomain.startsWith('.') || trimmedDomain.endsWith('.'))
    return false

  // 分割域名标签
  const labels = trimmedDomain.split('.')

  // 至少要有一个标签
  if (labels.length === 0)
    return false

  // 验证每个标签
  for (const label of labels) {
    // 标签不能为空
    if (label.length === 0)
      return false

    // 标签长度不能超过63字符
    if (label.length > 63)
      return false

    // 标签不能以连字符开头或结尾
    if (label.startsWith('-') || label.endsWith('-'))
      return false

    // 标签只能包含字母、数字和连字符
    if (!/^[a-z0-9-]+$/i.test(label))
      return false
  }

  return true
}

/**
 * 验证DNS服务器地址是否合法
 * @param dns 要验证的DNS服务器地址字符串
 * @returns 是否为合法的DNS服务器地址
 */
export const isValidDNS = (dns: string): boolean => {
  // DNS服务器地址可以是IPv4地址、IPv6地址或域名
  if (!dns || dns.trim() === '')
    return false

  const trimmedDns = dns.trim()

  // 首先检查是否为IPv4地址
  if (isValidIPv4(trimmedDns)) {
    // 排除一些无效的DNS地址
    const parts = trimmedDns.split('.').map(Number)
    const [first] = parts

    // 排除0.0.0.0、127.x.x.x（本地回环）、***************（广播地址）
    if (trimmedDns === '0.0.0.0' || trimmedDns === '***************' || first === 127)
      return false

    // 排除组播地址范围 *********-***************
    if (first >= 224 && first <= 239)
      return false

    return true
  }

  // 检查是否为IPv6地址
  if (isValidIPv6(trimmedDns)) {
    // 排除一些无效的IPv6 DNS地址
    const lowerDns = trimmedDns.toLowerCase()

    // 排除本地回环地址
    if (lowerDns === '::1')
      return false

    // 排除未指定地址
    if (lowerDns === '::')
      return false

    return true
  }

  // 检查是否为有效域名
  if (isValidDomain(trimmedDns)) {
    // 域名作为DNS服务器地址是有效的
    return true
  }

  return false
}

/**
 * 验证TTL值是否合法
 * @param ttl TTL值（秒）
 * @returns 是否为合法的TTL值
 */
export const isValidTTL = (ttl: number | string): boolean => {
  const numTtl = typeof ttl === 'string' ? Number.parseInt(ttl, 10) : ttl

  // TTL必须是非负整数，最大值为2^31-1（2147483647）
  return !Number.isNaN(numTtl) && numTtl >= 0 && numTtl <= 2147483647
}

/**
 * 验证MX记录优先级是否合法
 * @param priority MX记录优先级
 * @returns 是否为合法的优先级
 */
export const isValidMXPriority = (priority: number | string): boolean => {
  const numPriority = typeof priority === 'string' ? Number.parseInt(priority, 10) : priority

  // MX优先级必须是0-65535之间的整数
  return !Number.isNaN(numPriority) && numPriority >= 0 && numPriority <= 65535
}

/**
 * 验证DNS A记录是否合法
 * @param record A记录的IP地址
 * @returns 是否为合法的A记录
 */
export const isValidARecord = (record: string): boolean => {
  if (!record || record.trim() === '')
    return false

  const trimmedRecord = record.trim()

  // A记录必须是有效的IPv4地址
  if (!isValidIPv4(trimmedRecord))
    return false

  // 排除私有IP地址（根据需求可调整）
  const parts = trimmedRecord.split('.').map(Number)
  const [first] = parts

  // 排除本地回环地址、0.0.0.0和***************
  return !(first === 127 || trimmedRecord === '0.0.0.0' || trimmedRecord === '***************')
}

/**
 * 验证DNS AAAA记录是否合法
 * @param record AAAA记录的IPv6地址
 * @returns 是否为合法的AAAA记录
 */
export const isValidAAAARecord = (record: string): boolean => {
  if (!record || record.trim() === '')
    return false

  const trimmedRecord = record.trim()

  // AAAA记录必须是有效的IPv6地址
  if (!isValidIPv6(trimmedRecord))
    return false

  const lowerRecord = trimmedRecord.toLowerCase()

  // 排除本地回环地址和未指定地址
  return !(lowerRecord === '::1' || lowerRecord === '::')
}

/**
 * 验证DNS CNAME记录是否合法
 * @param record CNAME记录的目标域名
 * @returns 是否为合法的CNAME记录
 */
export const isValidCNAMERecord = (record: string): boolean => {
  if (!record || record.trim() === '')
    return false

  const trimmedRecord = record.trim()

  // CNAME记录必须是有效的域名
  return isValidDomain(trimmedRecord)
}

/**
 * 验证DNS MX记录是否合法
 * @param priority MX记录优先级
 * @param mailServer 邮件服务器域名
 * @returns 是否为合法的MX记录
 */
export const isValidMXRecord = (priority: number | string, mailServer: string): boolean => {
  // 验证优先级
  if (!isValidMXPriority(priority))
    return false

  // 验证邮件服务器域名
  if (!mailServer || mailServer.trim() === '')
    return false

  return isValidDomain(mailServer.trim())
}

/**
 * 验证DNS TXT记录是否合法
 * @param record TXT记录内容
 * @returns 是否为合法的TXT记录
 */
export const isValidTXTRecord = (record: string): boolean => {
  if (!record)
    return false

  // TXT记录可以包含任意字符串，但每个段不能超过255字节
  // 这里进行基本的长度检查
  const segments = record.split('"').filter(segment => segment.length > 0)

  for (const segment of segments) {
    // 每个段的长度不能超过255字节
    if (new TextEncoder().encode(segment).length > 255)
      return false
  }

  return true
}

/**
 * 验证DNS NS记录是否合法
 * @param record NS记录的名称服务器域名
 * @returns 是否为合法的NS记录
 */
export const isValidNSRecord = (record: string): boolean => {
  if (!record || record.trim() === '')
    return false

  const trimmedRecord = record.trim()

  // NS记录必须是有效的域名，且通常应该是FQDN（完全限定域名）
  if (!isValidDomain(trimmedRecord))
    return false

  // NS记录通常应该包含至少一个点（即不是单标签域名）
  return trimmedRecord.includes('.')
}

export const formatTraffic = (bytes: number): string => {
  if (bytes < 1024)
    return `${bytes} B`
  const units = ['KB', 'MB', 'GB', 'TB', 'PB']
  let value = bytes / 1024
  let unitIndex = 0
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }

  return `${value.toFixed(2)} ${units[unitIndex]}`
}
